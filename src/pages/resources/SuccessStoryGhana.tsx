import React from 'react';
import { Link } from 'react-router-dom';
import { ArrowLeft, Calendar, MapPin, TrendingUp, Users, DollarSign, CheckCircle, Quote } from 'lucide-react';

const SuccessStoryGhana = () => {
  const timeline = [
    {
      year: "2019",
      title: "The Dream",
      description: "<PERSON><PERSON><PERSON>, a software engineer in Atlanta, decides to return to Ghana and start an agribusiness focused on sustainable cocoa farming."
    },
    {
      year: "2020",
      title: "Market Research",
      description: "Partnered with AfriPath to conduct comprehensive market analysis and identify opportunities in Ghana's agricultural sector."
    },
    {
      year: "2021",
      title: "Business Formation",
      description: "Registered Golden Harvest Agribusiness Ltd. in Ghana with support for legal compliance and local partnerships."
    },
    {
      year: "2022",
      title: "Operations Launch",
      description: "Acquired 500 acres of farmland and began operations with 50 local farmers using sustainable farming practices."
    },
    {
      year: "2023",
      title: "Scaling Up",
      description: "Expanded to 1,200 acres, partnered with international buyers, and launched direct-trade cocoa export operations."
    },
    {
      year: "2024",
      title: "Thriving Business",
      description: "Generated $2.8M in revenue, employed 150+ people, and became a certified organic cocoa supplier to European markets."
    }
  ];

  const keyMetrics = [
    { metric: "$2.8M", description: "Annual Revenue (2024)", icon: <DollarSign className="h-6 w-6" /> },
    { metric: "150+", description: "Local Jobs Created", icon: <Users className="h-6 w-6" /> },
    { metric: "1,200", description: "Acres Under Management", icon: <MapPin className="h-6 w-6" /> },
    { metric: "300%", description: "ROI in 5 Years", icon: <TrendingUp className="h-6 w-6" /> }
  ];

  const challenges = [
    {
      challenge: "Land Acquisition",
      solution: "Worked with local chiefs and legal advisors to secure proper land titles and community buy-in."
    },
    {
      challenge: "Farmer Training",
      solution: "Invested in comprehensive training programs for sustainable farming practices and quality control."
    },
    {
      challenge: "Market Access",
      solution: "Built relationships with international buyers and obtained organic certification for premium markets."
    },
    {
      challenge: "Financial Management",
      solution: "Established local banking relationships and implemented robust financial controls and reporting systems."
    }
  ];

  const successFactors = [
    "Strong local partnerships and community engagement",
    "Focus on sustainable and ethical business practices",
    "Investment in farmer education and capacity building",
    "Strategic positioning in high-value export markets",
    "Leveraging diaspora networks for market access"
  ];

  return (
    <div className="min-h-screen bg-gray-200">
      {/* Header */}
      <div className="bg-[#2d5016] text-white py-16">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <Link
            to="/services"
            className="inline-flex items-center text-[#f4a261] hover:text-white transition-colors duration-200 mb-6"
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Services
          </Link>
          
          <div className="flex items-center text-sm text-gray-300 mb-4">
            <Calendar className="h-4 w-4 mr-2" />
            <span>January 2, 2025 • By admin • 8 min read</span>
          </div>
          
          <h1 className="text-4xl md:text-5xl font-bold mb-6">
            From Dream to Reality: How One Diaspora Entrepreneur Built a Thriving Business in Ghana
          </h1>
          
          <p className="text-xl text-gray-200 leading-relaxed">
            Follow Kwame's inspiring journey from software engineer in Atlanta to successful agribusiness owner in Ghana, creating jobs and impact across communities.
          </p>
        </div>
      </div>

      {/* Content */}
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
        {/* Introduction */}
        <section className="mb-16">
          <div className="bg-white rounded-xl p-8 shadow-lg">
            <div className="flex items-start">
              <Quote className="h-8 w-8 text-[#f4a261] mr-4 flex-shrink-0 mt-1" />
              <div>
                <p className="text-lg text-gray-700 italic leading-relaxed mb-4">
                  "I always knew I wanted to contribute to Ghana's development, but I didn't know where to start. AfriPath didn't just help me start a business – they helped me build a legacy that's creating real impact in rural communities."
                </p>
                <div className="text-[#2d5016] font-semibold">
                  Kwame Asante, Founder & CEO, Golden Harvest Agribusiness Ltd.
                </div>
                <div className="text-gray-500 text-sm">
                  Former Software Engineer, Atlanta → Agribusiness Entrepreneur, Ghana
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* Key Metrics */}
        <section className="mb-16">
          <h2 className="text-3xl font-bold text-[#2d5016] mb-8">Business Impact</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {keyMetrics.map((metric, index) => (
              <div key={index} className="bg-white rounded-xl p-6 shadow-lg text-center">
                <div className="flex justify-center mb-3 text-[#2d5016]">
                  {metric.icon}
                </div>
                <div className="text-3xl font-bold text-[#2d5016] mb-2">{metric.metric}</div>
                <div className="text-gray-600 text-sm">{metric.description}</div>
              </div>
            ))}
          </div>
        </section>

        {/* Journey Timeline */}
        <section className="mb-16">
          <h2 className="text-3xl font-bold text-[#2d5016] mb-8">The Journey</h2>
          <div className="space-y-8">
            {timeline.map((milestone, index) => (
              <div key={index} className="flex items-start">
                <div className="flex-shrink-0 mr-6">
                  <div className="w-16 h-16 bg-[#2d5016] text-white rounded-full flex items-center justify-center font-bold">
                    {milestone.year}
                  </div>
                </div>
                <div className="bg-white rounded-xl p-6 shadow-lg flex-grow">
                  <h3 className="text-xl font-semibold text-[#2d5016] mb-2">{milestone.title}</h3>
                  <p className="text-gray-700">{milestone.description}</p>
                </div>
              </div>
            ))}
          </div>
        </section>

        {/* Challenges and Solutions */}
        <section className="mb-16">
          <h2 className="text-3xl font-bold text-[#2d5016] mb-8">Overcoming Challenges</h2>
          <div className="space-y-6">
            {challenges.map((item, index) => (
              <div key={index} className="bg-white rounded-xl p-6 shadow-lg">
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                  <div>
                    <h3 className="text-lg font-semibold text-red-600 mb-2">Challenge: {item.challenge}</h3>
                  </div>
                  <div>
                    <h3 className="text-lg font-semibold text-green-600 mb-2">Solution:</h3>
                    <p className="text-gray-700">{item.solution}</p>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </section>

        {/* Success Factors */}
        <section className="mb-16">
          <h2 className="text-3xl font-bold text-[#2d5016] mb-8">Keys to Success</h2>
          <div className="bg-white rounded-xl p-8 shadow-lg">
            <div className="space-y-4">
              {successFactors.map((factor, index) => (
                <div key={index} className="flex items-start">
                  <CheckCircle className="h-6 w-6 text-green-600 mr-3 mt-1 flex-shrink-0" />
                  <span className="text-gray-700">{factor}</span>
                </div>
              ))}
            </div>
          </div>
        </section>

        {/* Impact Story */}
        <section className="mb-16">
          <h2 className="text-3xl font-bold text-[#2d5016] mb-8">Community Impact</h2>
          <div className="bg-white rounded-xl p-8 shadow-lg">
            <p className="text-gray-700 leading-relaxed mb-6">
              Golden Harvest Agribusiness has transformed not just Kwame's life, but entire communities in Ghana's Ashanti Region. 
              The company's sustainable farming practices have increased crop yields by 40% while preserving soil health for future generations.
            </p>
            <p className="text-gray-700 leading-relaxed mb-6">
              Beyond direct employment, the business has created a ripple effect of economic activity. Local suppliers, transport 
              companies, and service providers have all benefited from the increased economic activity. The company's farmer training 
              programs have equipped over 300 farmers with modern agricultural techniques, improving their livelihoods and food security.
            </p>
            <p className="text-gray-700 leading-relaxed">
              "What started as a personal dream has become a catalyst for community development," says Kwame. "We're not just 
              exporting cocoa – we're exporting hope, skills, and sustainable prosperity."
            </p>
          </div>
        </section>

        {/* Lessons Learned */}
        <section className="mb-16">
          <h2 className="text-3xl font-bold text-[#2d5016] mb-8">Lessons for Other Diaspora Entrepreneurs</h2>
          <div className="bg-[#f4a261] bg-opacity-10 rounded-xl p-8">
            <div className="space-y-4">
              <div className="flex items-start">
                <div className="w-2 h-2 bg-[#f4a261] rounded-full mt-2 mr-3 flex-shrink-0"></div>
                <p className="text-gray-700"><strong>Start with thorough research:</strong> Understanding the market, regulations, and cultural context is crucial for success.</p>
              </div>
              <div className="flex items-start">
                <div className="w-2 h-2 bg-[#f4a261] rounded-full mt-2 mr-3 flex-shrink-0"></div>
                <p className="text-gray-700"><strong>Build genuine partnerships:</strong> Success in Africa is relationship-driven. Invest time in building trust with local partners.</p>
              </div>
              <div className="flex items-start">
                <div className="w-2 h-2 bg-[#f4a261] rounded-full mt-2 mr-3 flex-shrink-0"></div>
                <p className="text-gray-700"><strong>Focus on impact:</strong> Businesses that create positive social and environmental impact are more sustainable and profitable.</p>
              </div>
              <div className="flex items-start">
                <div className="w-2 h-2 bg-[#f4a261] rounded-full mt-2 mr-3 flex-shrink-0"></div>
                <p className="text-gray-700"><strong>Be patient and persistent:</strong> Building a successful business in Africa takes time, but the rewards are worth the effort.</p>
              </div>
            </div>
          </div>
        </section>

        {/* Call to Action */}
        <div className="bg-[#2d5016] text-white rounded-xl p-8 text-center">
          <h3 className="text-2xl font-bold mb-4">Ready to Write Your Own Success Story?</h3>
          <p className="text-gray-200 mb-6">
            Get the same expert guidance and support that helped Kwame build his thriving business in Ghana.
          </p>
          <Link
            to="/book-consultation"
            className="inline-flex items-center bg-[#f4a261] text-[#2d5016] px-6 py-3 rounded-lg font-semibold hover:bg-[#e76f51] transition-colors duration-200"
          >
            Start Your Journey Today
          </Link>
        </div>
      </div>
    </div>
  );
};

export default SuccessStoryGhana;
