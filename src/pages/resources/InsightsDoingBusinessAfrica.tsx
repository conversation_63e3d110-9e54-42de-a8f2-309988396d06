import React from 'react';
import { Link } from 'react-router-dom';
import { ArrowLeft, Calendar, TrendingUp, AlertTriangle, CheckCircle, Globe, Users, Building, DollarSign } from 'lucide-react';

const InsightsDoingBusinessAfrica = () => {
  const keyInsights = [
    {
      icon: <Globe className="h-8 w-8 text-[#2d5016]" />,
      title: "Market Diversity",
      description: "Africa comprises 54 distinct markets, each with unique regulations, cultures, and business practices. Success requires country-specific strategies."
    },
    {
      icon: <Users className="h-8 w-8 text-[#2d5016]" />,
      title: "Relationship-Driven Business",
      description: "Personal relationships and trust are fundamental to business success. Invest time in building genuine connections with local partners."
    },
    {
      icon: <Building className="h-8 w-8 text-[#2d5016]" />,
      title: "Infrastructure Considerations",
      description: "Infrastructure varies significantly across regions. Factor in logistics, power supply, and connectivity when planning operations."
    },
    {
      icon: <DollarSign className="h-8 w-8 text-[#2d5016]" />,
      title: "Currency and Financial Systems",
      description: "Understand local banking systems, currency stability, and foreign exchange regulations for smooth financial operations."
    }
  ];

  const challenges = [
    "Regulatory complexity and bureaucracy",
    "Infrastructure limitations in some regions",
    "Currency volatility and exchange controls",
    "Cultural and language barriers",
    "Access to reliable local partners"
  ];

  const opportunities = [
    "Rapidly growing middle class across the continent",
    "Increasing digital adoption and mobile penetration",
    "Government incentives for foreign investment",
    "Untapped markets with high growth potential",
    "Strong diaspora networks for market entry"
  ];

  const successFactors = [
    {
      title: "Local Partnerships",
      description: "Partner with established local businesses or individuals who understand the market dynamics and regulatory environment."
    },
    {
      title: "Cultural Intelligence",
      description: "Invest in understanding local customs, business etiquette, and communication styles to build trust and credibility."
    },
    {
      title: "Regulatory Compliance",
      description: "Ensure full compliance with local laws, tax requirements, and business registration processes from day one."
    },
    {
      title: "Gradual Market Entry",
      description: "Start with pilot projects or smaller investments to test the market before scaling up operations."
    },
    {
      title: "Long-term Perspective",
      description: "African markets often require patience and persistence. Focus on building sustainable, long-term business relationships."
    }
  ];

  return (
    <div className="min-h-screen bg-gray-200">
      {/* Header */}
      <div className="bg-[#2d5016] text-white py-16">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <Link
            to="/services"
            className="inline-flex items-center text-[#f4a261] hover:text-white transition-colors duration-200 mb-6"
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Services
          </Link>
          
          <div className="flex items-center text-sm text-gray-300 mb-4">
            <Calendar className="h-4 w-4 mr-2" />
            <span>May 20, 2025 • By admin • 12 min read</span>
          </div>
          
          <h1 className="text-4xl md:text-5xl font-bold mb-6">
            Insights on Doing Business in Africa: What Diaspora Investors Need to Know
          </h1>
          
          <p className="text-xl text-gray-200 leading-relaxed">
            Navigate the complexities and unlock the opportunities of African markets with expert insights tailored for diaspora entrepreneurs and investors.
          </p>
        </div>
      </div>

      {/* Content */}
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
        {/* Introduction */}
        <div className="prose prose-lg max-w-none mb-12">
          <p className="text-gray-700 leading-relaxed text-lg">
            Africa represents one of the world's most dynamic and fastest-growing economic regions, with a combined GDP of over $3 trillion and a population exceeding 1.4 billion people. For diaspora investors, the continent offers unprecedented opportunities, but success requires understanding the unique business landscape, cultural nuances, and regulatory frameworks that define each market.
          </p>
        </div>

        {/* Key Insights */}
        <section className="mb-16">
          <h2 className="text-3xl font-bold text-[#2d5016] mb-8">Key Business Insights</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            {keyInsights.map((insight, index) => (
              <div key={index} className="bg-white rounded-xl p-6 shadow-lg">
                <div className="flex items-start">
                  <div className="flex-shrink-0 mr-4">
                    {insight.icon}
                  </div>
                  <div>
                    <h3 className="text-xl font-semibold text-[#2d5016] mb-3">{insight.title}</h3>
                    <p className="text-gray-700">{insight.description}</p>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </section>

        {/* Challenges and Opportunities */}
        <section className="mb-16">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            {/* Challenges */}
            <div className="bg-white rounded-xl p-8 shadow-lg">
              <div className="flex items-center mb-6">
                <AlertTriangle className="h-6 w-6 text-red-600 mr-3" />
                <h3 className="text-2xl font-bold text-[#2d5016]">Common Challenges</h3>
              </div>
              <ul className="space-y-3">
                {challenges.map((challenge, index) => (
                  <li key={index} className="flex items-start">
                    <div className="w-2 h-2 bg-red-600 rounded-full mt-2 mr-3 flex-shrink-0"></div>
                    <span className="text-gray-700">{challenge}</span>
                  </li>
                ))}
              </ul>
            </div>

            {/* Opportunities */}
            <div className="bg-white rounded-xl p-8 shadow-lg">
              <div className="flex items-center mb-6">
                <TrendingUp className="h-6 w-6 text-green-600 mr-3" />
                <h3 className="text-2xl font-bold text-[#2d5016]">Key Opportunities</h3>
              </div>
              <ul className="space-y-3">
                {opportunities.map((opportunity, index) => (
                  <li key={index} className="flex items-start">
                    <div className="w-2 h-2 bg-green-600 rounded-full mt-2 mr-3 flex-shrink-0"></div>
                    <span className="text-gray-700">{opportunity}</span>
                  </li>
                ))}
              </ul>
            </div>
          </div>
        </section>

        {/* Success Factors */}
        <section className="mb-16">
          <h2 className="text-3xl font-bold text-[#2d5016] mb-8">Critical Success Factors</h2>
          <div className="space-y-6">
            {successFactors.map((factor, index) => (
              <div key={index} className="bg-white rounded-xl p-6 shadow-lg">
                <div className="flex items-start">
                  <CheckCircle className="h-6 w-6 text-green-600 mr-4 mt-1 flex-shrink-0" />
                  <div>
                    <h3 className="text-xl font-semibold text-[#2d5016] mb-2">{factor.title}</h3>
                    <p className="text-gray-700">{factor.description}</p>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </section>

        {/* Call to Action */}
        <div className="bg-[#2d5016] text-white rounded-xl p-8 text-center">
          <h3 className="text-2xl font-bold mb-4">Ready to Start Your African Business Journey?</h3>
          <p className="text-gray-200 mb-6">
            Get personalized guidance and market insights tailored to your investment goals and target markets.
          </p>
          <Link
            to="/book-consultation"
            className="inline-flex items-center bg-[#f4a261] text-[#2d5016] px-6 py-3 rounded-lg font-semibold hover:bg-[#e76f51] transition-colors duration-200"
          >
            Schedule Your Consultation
          </Link>
        </div>
      </div>
    </div>
  );
};

export default InsightsDoingBusinessAfrica;
