import React from 'react';
import { Link } from 'react-router-dom';
import { ArrowLeft, Calendar, TrendingUp, Smartphone, Leaf, Home, DollarSign, Users, BarChart3 } from 'lucide-react';

const DiasporaInvestmentTrends = () => {
  const trends = [
    {
      rank: 1,
      icon: <Smartphone className="h-12 w-12 text-[#2d5016]" />,
      title: "Digital Technology & Fintech",
      growth: "+45%",
      investment: "$2.8B",
      description: "Diaspora investors are leading the charge in Africa's digital transformation, with significant investments in fintech, e-commerce, and digital services.",
      keyAreas: [
        "Mobile payment solutions",
        "Digital banking platforms",
        "E-commerce marketplaces",
        "EdTech and HealthTech platforms",
        "Blockchain and cryptocurrency"
      ],
      examples: [
        "Flutterwave (Nigeria) - $250M+ raised",
        "Paystack (Nigeria) - Acquired by Stripe for $200M",
        "Chipper Cash (Ghana/Uganda) - $150M+ raised"
      ],
      whyTrending: "Africa's mobile-first economy and growing internet penetration create massive opportunities for digital solutions. Diaspora investors bring both capital and global tech expertise."
    },
    {
      rank: 2,
      icon: <Leaf className="h-12 w-12 text-[#2d5016]" />,
      title: "Sustainable Agriculture & Food Security",
      growth: "+38%",
      investment: "$1.9B",
      description: "Climate-smart agriculture and food processing are attracting diaspora investment as Africa seeks to feed its growing population while adapting to climate change.",
      keyAreas: [
        "Precision farming technologies",
        "Agri-processing and value addition",
        "Cold chain and logistics",
        "Sustainable farming practices",
        "Agricultural financing platforms"
      ],
      examples: [
        "Twiga Foods (Kenya) - $50M+ raised",
        "Apollo Agriculture (Kenya) - $40M+ raised",
        "Farmcrowdy (Nigeria) - Digital farming platform"
      ],
      whyTrending: "Growing food demand, climate challenges, and government support for agricultural modernization create compelling investment opportunities."
    },
    {
      rank: 3,
      icon: <Home className="h-12 w-12 text-[#2d5016]" />,
      title: "Real Estate & Urban Development",
      growth: "+32%",
      investment: "$3.2B",
      description: "Rapid urbanization and growing middle class are driving diaspora investment in residential, commercial, and mixed-use developments across major African cities.",
      keyAreas: [
        "Affordable housing projects",
        "Commercial real estate",
        "Mixed-use developments",
        "PropTech solutions",
        "Infrastructure development"
      ],
      examples: [
        "Rendeavour (Multiple countries) - $2B+ invested",
        "Estate Intel (Nigeria) - PropTech platform",
        "Tatu City (Kenya) - Mixed-use development"
      ],
      whyTrending: "Africa's urbanization rate of 4% annually creates massive demand for housing and commercial spaces, especially in tier-1 cities."
    }
  ];

  const marketStats = [
    { metric: "$8.2B", description: "Total diaspora investment in 2024", icon: <DollarSign className="h-6 w-6" /> },
    { metric: "54%", description: "Year-over-year growth in diaspora FDI", icon: <TrendingUp className="h-6 w-6" /> },
    { metric: "2.3M", description: "Diaspora investors actively investing", icon: <Users className="h-6 w-6" /> },
    { metric: "28", description: "African countries receiving diaspora investment", icon: <BarChart3 className="h-6 w-6" /> }
  ];

  const investmentTips = [
    {
      title: "Start with Market Research",
      description: "Conduct thorough due diligence on your target market, including regulatory environment, competition, and local partnerships."
    },
    {
      title: "Leverage Diaspora Networks",
      description: "Connect with other diaspora investors and local business communities to share insights and identify opportunities."
    },
    {
      title: "Consider Impact Investing",
      description: "Align your investments with social and environmental impact goals to access additional funding and support."
    },
    {
      title: "Plan for Long-term Growth",
      description: "African markets often require patience and persistence. Focus on sustainable, long-term value creation."
    }
  ];

  return (
    <div className="min-h-screen bg-gray-200">
      {/* Header */}
      <div className="bg-[#2d5016] text-white py-16">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <Link
            to="/services"
            className="inline-flex items-center text-[#f4a261] hover:text-white transition-colors duration-200 mb-6"
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Services
          </Link>
          
          <div className="flex items-center text-sm text-gray-300 mb-4">
            <Calendar className="h-4 w-4 mr-2" />
            <span>May 20, 2025 • By admin • 10 min read</span>
          </div>
          
          <h1 className="text-4xl md:text-5xl font-bold mb-6">
            Top 3 Diaspora Investment Trends in Africa: 2025 Edition
          </h1>
          
          <p className="text-xl text-gray-200 leading-relaxed">
            Discover the hottest investment sectors attracting diaspora capital and driving economic transformation across the African continent.
          </p>
        </div>
      </div>

      {/* Content */}
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
        {/* Market Overview */}
        <section className="mb-16">
          <h2 className="text-3xl font-bold text-[#2d5016] mb-8">2025 Market Overview</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            {marketStats.map((stat, index) => (
              <div key={index} className="bg-white rounded-xl p-6 shadow-lg text-center">
                <div className="flex justify-center mb-3 text-[#2d5016]">
                  {stat.icon}
                </div>
                <div className="text-3xl font-bold text-[#2d5016] mb-2">{stat.metric}</div>
                <div className="text-gray-600 text-sm">{stat.description}</div>
              </div>
            ))}
          </div>
          <p className="text-gray-700 leading-relaxed text-lg">
            The African diaspora investment landscape has reached unprecedented levels in 2025, with record-breaking capital flows 
            into key sectors driving economic transformation across the continent. These trends reflect both the growing confidence 
            of diaspora investors and the maturation of African markets.
          </p>
        </section>

        {/* Investment Trends */}
        <section className="mb-16">
          <h2 className="text-3xl font-bold text-[#2d5016] mb-8">Top Investment Trends</h2>
          <div className="space-y-12">
            {trends.map((trend, index) => (
              <div key={index} className="bg-white rounded-xl p-8 shadow-lg">
                <div className="flex items-start mb-6">
                  <div className="flex-shrink-0 mr-6">
                    <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center">
                      {trend.icon}
                    </div>
                  </div>
                  <div className="flex-grow">
                    <div className="flex items-center mb-2">
                      <span className="bg-[#2d5016] text-white px-3 py-1 rounded-full text-sm font-bold mr-3">
                        #{trend.rank}
                      </span>
                      <span className="bg-green-100 text-green-800 px-3 py-1 rounded-full text-sm font-semibold">
                        {trend.growth} growth
                      </span>
                    </div>
                    <h3 className="text-2xl font-bold text-[#2d5016] mb-2">{trend.title}</h3>
                    <p className="text-gray-600 mb-4">{trend.description}</p>
                    <div className="text-sm text-gray-500 mb-4">
                      <strong>Total Investment:</strong> {trend.investment}
                    </div>
                  </div>
                </div>

                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                  <div>
                    <h4 className="font-semibold text-[#2d5016] mb-3">Key Investment Areas:</h4>
                    <ul className="space-y-2">
                      {trend.keyAreas.map((area, areaIndex) => (
                        <li key={areaIndex} className="flex items-start">
                          <div className="w-2 h-2 bg-[#f4a261] rounded-full mt-2 mr-3 flex-shrink-0"></div>
                          <span className="text-gray-700 text-sm">{area}</span>
                        </li>
                      ))}
                    </ul>
                  </div>
                  <div>
                    <h4 className="font-semibold text-[#2d5016] mb-3">Success Stories:</h4>
                    <ul className="space-y-2">
                      {trend.examples.map((example, exampleIndex) => (
                        <li key={exampleIndex} className="text-gray-700 text-sm">
                          • {example}
                        </li>
                      ))}
                    </ul>
                  </div>
                </div>

                <div className="mt-6 p-4 bg-gray-50 rounded-lg">
                  <h4 className="font-semibold text-[#2d5016] mb-2">Why This Trend Matters:</h4>
                  <p className="text-gray-700 text-sm">{trend.whyTrending}</p>
                </div>
              </div>
            ))}
          </div>
        </section>

        {/* Investment Tips */}
        <section className="mb-16">
          <h2 className="text-3xl font-bold text-[#2d5016] mb-8">Investment Tips for Diaspora Investors</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {investmentTips.map((tip, index) => (
              <div key={index} className="bg-white rounded-xl p-6 shadow-lg">
                <h3 className="text-lg font-semibold text-[#2d5016] mb-3">{tip.title}</h3>
                <p className="text-gray-700">{tip.description}</p>
              </div>
            ))}
          </div>
        </section>

        {/* Call to Action */}
        <div className="bg-[#2d5016] text-white rounded-xl p-8 text-center">
          <h3 className="text-2xl font-bold mb-4">Ready to Join These Investment Trends?</h3>
          <p className="text-gray-200 mb-6">
            Get expert guidance on identifying and evaluating investment opportunities in Africa's hottest sectors.
          </p>
          <Link
            to="/book-consultation"
            className="inline-flex items-center bg-[#f4a261] text-[#2d5016] px-6 py-3 rounded-lg font-semibold hover:bg-[#e76f51] transition-colors duration-200"
          >
            Schedule Investment Consultation
          </Link>
        </div>
      </div>
    </div>
  );
};

export default DiasporaInvestmentTrends;
