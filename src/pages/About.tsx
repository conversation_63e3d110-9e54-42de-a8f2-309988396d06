import React from 'react';
import { CheckCircle, Users, Globe, Shield, Target, Eye, Briefcase, MapPin, Award, TrendingUp, Heart, Lightbulb } from 'lucide-react';
import { useContent, getContentValue } from '../hooks/useContent';

const About = () => {
  const { content, loading, error } = useContent('about');

  // Afficher un loader pendant le chargement
  if (loading) {
    return (
      <div className="min-h-screen bg-gray-100 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-[#2d5016] mx-auto mb-4"></div>
          <p className="text-gray-600">Chargement du contenu...</p>
        </div>
      </div>
    );
  }

  if (error) {
    console.warn('Erreur de chargement du contenu:', error);
  }

  const values = [
    {
      icon: <Users className="h-8 w-8" />,
      title: 'Cultural Expertise',
      description: 'Deep understanding of both diaspora and African business cultures',
      color: 'bg-[#2d5016]'
    },
    {
      icon: <Shield className="h-8 w-8" />,
      title: 'Local Trust',
      description: 'Verified partnerships and trusted relationships across Africa',
      color: 'bg-[#f4a261]'
    },
    {
      icon: <Globe className="h-8 w-8" />,
      title: 'Transparency',
      description: 'Clear communication and honest guidance throughout your journey',
      color: 'bg-[#2d5016]'
    },
    {
      icon: <TrendingUp className="h-8 w-8" />,
      title: 'Proven Results',
      description: 'Track record of successful business launches and sustainable growth',
      color: 'bg-[#f4a261]'
    }
  ];

  const processSteps = [
    {
      step: '1',
      title: 'Discovery & Initial Consultation',
      description: 'We begin with a personalized strategy session to understand your goals. Together, we explore the best pathways for diaspora investment in Africa, discuss country and sector options, and lay the foundation for a smart entry plan.'
    },
    {
      step: '2',
      title: 'Market Intelligence & Strategy Design',
      description: 'We deliver a tailored market entry roadmap, including deep insights into regional trends, risk profiles, regulatory factors, and growth sectors. This strategic blueprint helps you take the right steps toward launching your business with clarity and confidence.'
    },
    {
      step: '3',
      title: 'Local Partnerships & On-the-Ground Support',
      description: 'Once the plan is set, we connect you to trusted local consultants, legal teams, and vendors who help navigate logistics, paperwork, and operations. Think of us as your boots on the ground—so you don\'t have to be.'
    },
    {
      step: '4',
      title: 'Legal Setup & Business Registration',
      description: 'Our team streamlines the African business registration process, handling permits, tax compliance, and official filings in your chosen country. We work closely with vetted legal partners to keep everything smooth, fast, and fully compliant.'
    },
    {
      step: '5',
      title: 'Long-Term Advisory & Trade Visits',
      description: 'We don\'t just launch businesses, we help grow them. Get ongoing advisory, regulatory updates, and access to curated trade visits where you\'ll meet local entrepreneurs, explore real opportunities, and strengthen your investment with local trust.'
    }
  ];

  return (
    <div className="py-16 bg-gray-200">
      {/* Hero Section */}
      <section className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 mb-20">


        <div className="text-center mb-16">
          <h1 className="text-4xl md:text-5xl font-bold text-[#2d5016] mb-8">
            {getContentValue(content, 'hero', 'title', 'About Us')}
          </h1>
          <p className="text-xl text-gray-600 leading-relaxed max-w-4xl mx-auto">
            {getContentValue(content, 'hero', 'description', 'AfriPath Consulting is a diaspora-led firm dedicated to helping individuals and organizations confidently invest and do business in Africa. Founded by Linda-Ann Akanvou Numfor, a diaspora professional passionate about bridging the gap between African opportunity and diaspora ambition, AfriPath offers strategic guidance, expert connections, and practical tools for success.')}
          </p>
        </div>

        <div className="text-center mb-16">
          <p className="text-lg text-gray-700 max-w-4xl mx-auto leading-relaxed mb-6">
            Based in key African markets including Ghana, Kenya, Cameroon, Senegal, and Côte d'Ivoire, we provide tailored support for market entry, business registration, compliance, trade visits, and more. Our approach is rooted in cultural fluency, empathy, and real-world experience empowering diaspora entrepreneurs to move from intention to action without the overwhelm.
          </p>
          <p className="text-lg text-gray-700 max-w-4xl mx-auto leading-relaxed">
            Whether you're launching a startup, exploring partnerships, or scaling an existing venture, AfriPath offers end-to-end support to help you make informed, confident decisions. We're not just consultants, we're your trusted pathway to doing business in Africa, smarter and stronger.
          </p>
        </div>
      </section>

      {/* Mission, Vision, What We Do Section */}
      <section className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {/* Mission */}
            <div className="bg-gray-50 p-8 rounded-xl hover:shadow-lg transition-shadow duration-300">
              <div className="flex items-center mb-4">
                <div className="bg-[#2d5016] p-3 rounded-lg mr-4">
                  <Target className="h-6 w-6 text-white" />
                </div>
                <h3 className="text-2xl font-bold text-[#2d5016]">{getContentValue(content, 'mission', 'title', 'Our Mission')}</h3>
              </div>
              <p className="text-gray-700 leading-relaxed">
                {getContentValue(content, 'mission', 'description', 'Our mission is to empower the African diaspora with trusted guidance, local expertise, and strategic tools to confidently launch and grow sustainable businesses across the continent, bridging dreams with real opportunities.')}
              </p>
            </div>

            {/* Vision */}
            <div className="bg-gray-50 p-8 rounded-xl hover:shadow-lg transition-shadow duration-300">
              <div className="flex items-center mb-4">
                <div className="bg-[#f4a261] p-3 rounded-lg mr-4">
                  <Eye className="h-6 w-6 text-white" />
                </div>
                <h3 className="text-2xl font-bold text-[#2d5016]">{getContentValue(content, 'vision', 'title', 'Our Vision')}</h3>
              </div>
              <p className="text-gray-700 leading-relaxed">
                {getContentValue(content, 'vision', 'description', 'Our vision is to become the leading consulting firm connecting the global African diaspora with vibrant, thriving markets in Africa, fostering economic growth, innovation, and lasting impact across borders.')}
              </p>
            </div>

            {/* What We Do */}
            <div className="bg-gray-50 p-8 rounded-xl hover:shadow-lg transition-shadow duration-300">
              <div className="flex items-center mb-4">
                <div className="bg-[#2d5016] p-3 rounded-lg mr-4">
                  <Briefcase className="h-6 w-6 text-white" />
                </div>
                <h3 className="text-2xl font-bold text-[#2d5016]">What We Do</h3>
              </div>
              <p className="text-gray-700 leading-relaxed">
                Africa is one of the fastest-growing markets in the world, offering bold opportunities across sectors like agribusiness, tech, energy, and real estate. AfriPath provides end-to-end support to help diaspora and global investors enter these markets with clarity, confidence, and cultural insight.
              </p>
            </div>
          </div>
        </div>
      </section>


      {/* About Image Section */}
      <section className="py-16 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            {/* Left Column - Image */}
            <div className="relative">
              <img
                src="/about.png"
                alt="About AfriPath Consulting"
                className="w-full h-80 object-cover rounded-xl shadow-lg"
              />
              {/* Overlay Badge */}
              <div className="absolute top-6 left-6">
                <div className="inline-flex items-center bg-gradient-to-r from-[#2d5016] to-[#f4a261] text-white px-6 py-3 rounded-full shadow-2xl backdrop-blur-sm">
                  <div className="flex items-center space-x-3">
                    <div className="bg-white/20 backdrop-blur-sm rounded-full px-3 py-1">
                      <span className="text-2xl font-bold">10+</span>
                    </div>
                    <div className="text-left">
                      <div className="text-sm font-medium opacity-90">Years of</div>
                      <div className="text-lg font-bold">Excellence</div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Right Column - Text */}
            <div>
              <div className="flex items-center mb-6">
                <div className="bg-[#f4a261] p-3 rounded-lg mr-4">
                  <Heart className="h-8 w-8 text-white" />
                </div>
                <h2 className="text-3xl md:text-4xl font-bold text-[#2d5016]">
                  Our Mission
                </h2>
              </div>
              <div className="space-y-6">
                <div className="flex items-start">
                  <MapPin className="h-6 w-6 text-[#2d5016] mr-3 mt-1 flex-shrink-0" />
                  <p className="text-lg text-gray-700 leading-relaxed">
                    Based in key African markets including Ghana, Kenya, Cameroon, Senegal, and Côte d'Ivoire, we provide tailored support for market entry, business registration, compliance, trade visits, and more.
                  </p>
                </div>
                <div className="flex items-start">
                  <Lightbulb className="h-6 w-6 text-[#f4a261] mr-3 mt-1 flex-shrink-0" />
                  <p className="text-lg text-gray-700 leading-relaxed">
                    Our approach is rooted in cultural fluency, empathy, and real-world experience empowering diaspora entrepreneurs to move from intention to action without the overwhelm.
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Team Section */}
      <section className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-[#2d5016] mb-4">
              Our Team
            </h2>
            <p className="text-xl text-gray-600">
              Our team brings together diaspora professionals and local experts across Africa—uniting lived experience with on-the-ground insight to deliver personalized, practical, and culturally grounded business support.
            </p>
          </div>

          {/* Managing Director */}
          <div className="mb-16">
            <div className="bg-gray-50 rounded-xl p-8 max-w-2xl mx-auto text-center">
              <img
                src="/Linda-Ann.png"
                alt="Linda-Ann Numfor"
                className="w-32 h-32 rounded-full mx-auto mb-6 object-cover"
              />
              <h3 className="text-2xl font-bold text-[#2d5016] mb-2">Linda-Ann Numfor</h3>
              <p className="text-gray-600 mb-4">Managing Director, AfriPath Consulting</p>
            </div>
          </div>

          {/* Regional Teams */}
          <div className="space-y-12">
            {/* West Africa */}
            <div>
              <h3 className="text-2xl font-bold text-[#2d5016] text-center mb-8">West Africa</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-8 max-w-4xl mx-auto">
                <div className="bg-gray-50 rounded-xl p-6 text-center">
                  <img
                    src="/Akanvou Joelle.png"
                    alt="Joelle Akanvou"
                    className="w-24 h-24 rounded-full mx-auto mb-4 object-cover"
                  />
                  <h4 className="text-xl font-semibold text-[#2d5016] mb-2">Joelle Akanvou</h4>
                  <p className="text-gray-600">West Africa Regional Lead</p>
                </div>
                <div className="bg-gray-50 rounded-xl p-6 text-center">
                  <img
                    src="/Raphael Faye.png"
                    alt="Raphael Faye"
                    className="w-24 h-24 rounded-full mx-auto mb-4 object-cover"
                  />
                  <h4 className="text-xl font-semibold text-[#2d5016] mb-2">Raphael Faye</h4>
                  <p className="text-gray-600">West Africa Regional Lead</p>
                </div>
              </div>
            </div>

            {/* East Africa */}
            <div>
              <h3 className="text-2xl font-bold text-[#2d5016] text-center mb-8">East Africa</h3>
              <div className="max-w-md mx-auto">
                <div className="bg-gray-50 rounded-xl p-6 text-center">
                  <img
                    src="/Maureen Anyango.png"
                    alt="Maureen Anyango Oduor"
                    className="w-24 h-24 rounded-full mx-auto mb-4 object-cover"
                  />
                  <h4 className="text-xl font-semibold text-[#2d5016] mb-2">Maureen Anyango Oduor</h4>
                  <p className="text-gray-600">East Africa Business Development Lead</p>
                </div>
              </div>
            </div>

            {/* Southern Africa */}
            <div>
              <h3 className="text-2xl font-bold text-[#2d5016] text-center mb-8">Southern Africa</h3>
              <div className="max-w-md mx-auto">
                <div className="bg-gray-50 rounded-xl p-6 text-center">
                  <img
                    src="/Tinashe Moyo.jpg"
                    alt="Tinashe Moyo"
                    className="w-24 h-24 rounded-full mx-auto mb-4 object-cover"
                  />
                  <h4 className="text-xl font-semibold text-[#2d5016] mb-2">Tinashe Moyo</h4>
                  <p className="text-gray-600">Southern Africa Regional Lead</p>
                </div>
              </div>
            </div>

            {/* Central Africa */}
            <div>
              <h3 className="text-2xl font-bold text-[#2d5016] text-center mb-8">Central Africa</h3>
              <div className="max-w-md mx-auto">
                <div className="bg-gray-50 rounded-xl p-6 text-center">
                  <img
                    src="/Neba Paul.png"
                    alt="NEBA Paul Azinwi"
                    className="w-24 h-24 rounded-full mx-auto mb-4 object-cover"
                  />
                  <h4 className="text-xl font-semibold text-[#2d5016] mb-2">NEBA Paul Azinwi</h4>
                  <p className="text-gray-600">Central Africa Business & Digital Strategy Lead</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Values Section */}
      <section className="py-20 bg-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <div className="flex items-center justify-center mb-4">
              <div className="bg-[#2d5016] p-3 rounded-lg mr-4">
                <Award className="h-8 w-8 text-white" />
              </div>
              <h2 className="text-3xl md:text-4xl font-bold text-[#2d5016]">
                Our Core Values
              </h2>
            </div>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              The principles that guide our work and define our commitment to your success
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {values.map((value, index) => (
              <div
                key={index}
                className="bg-white p-6 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2 text-center"
              >
                <div className={`${value.color} p-4 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4`}>
                  <div className="text-white">
                    {value.icon}
                  </div>
                </div>
                <h3 className="text-xl font-semibold text-[#2d5016] mb-3">{value.title}</h3>
                <p className="text-gray-700 leading-relaxed">{value.description}</p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Process Steps Section */}
      <section className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <div className="flex items-center justify-center mb-4">
              <div className="bg-[#f4a261] p-3 rounded-lg mr-4">
                <CheckCircle className="h-8 w-8 text-white" />
              </div>
              <h2 className="text-3xl md:text-4xl font-bold text-[#2d5016]">
                Our Process
              </h2>
            </div>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              A proven 5-step approach to launching your African business venture
            </p>
          </div>

          <div className="space-y-8">
            {processSteps.map((step, index) => (
              <div
                key={index}
                className="flex items-start bg-gray-50 rounded-xl p-6 hover:shadow-lg transition-shadow duration-300"
              >
                <div className="flex-shrink-0 mr-6">
                  <div className="w-12 h-12 bg-[#2d5016] text-white rounded-full flex items-center justify-center font-bold text-lg">
                    {step.step}
                  </div>
                </div>
                <div>
                  <h3 className="text-xl font-semibold text-[#2d5016] mb-3">{step.title}</h3>
                  <p className="text-gray-700 leading-relaxed">{step.description}</p>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>
    </div>
  );
};

export default About;