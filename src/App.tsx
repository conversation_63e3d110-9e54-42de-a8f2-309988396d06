import React from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import Header from './components/Header';
import Footer from './components/Footer';
import ScrollToTop from './components/ScrollToTop';
import Home from './pages/Home';
import About from './pages/About';
import Services from './pages/Services';
import CountryProfiles from './pages/CountryProfiles';

import BookConsultation from './pages/BookConsultation';
import IvoryCoast from './pages/countries/IvoryCoast';
import Senegal from './pages/countries/Senegal';
import Morocco from './pages/countries/Morocco';
import Cameroon from './pages/countries/Cameroon';
import Kenya from './pages/countries/Kenya';
import Payment from './pages/Payment';
import Top5CountriesDiasporaInvestment from './pages/resources/Top5CountriesDiasporaInvestment';
import BusinessRegistrationGhanaNigeria from './pages/resources/BusinessRegistrationGhanaNigeria';
import DiasporaStartupsChangingAfrica from './pages/resources/DiasporaStartupsChangingAfrica';
import InsightsDoingBusinessAfrica from './pages/resources/InsightsDoingBusinessAfrica';
import DiasporaInvestmentTrends from './pages/resources/DiasporaInvestmentTrends';
import SuccessStoryGhana from './pages/resources/SuccessStoryGhana';
import PrivacyPolicy from './pages/PrivacyPolicy';
import TermsOfService from './pages/TermsOfService';
import Admin from './pages/Admin';

function App() {
  return (
    <Router>
      <ScrollToTop />
      <div className="min-h-screen bg-white">
        <Header />
        <main>
          <Routes>
            <Route path="/" element={<Home />} />
            <Route path="/about" element={<About />} />
            <Route path="/services" element={<Services />} />
            <Route path="/country-profiles" element={<CountryProfiles />} />
            <Route path="/country-profiles/ivory-coast" element={<IvoryCoast />} />
            <Route path="/country-profiles/senegal" element={<Senegal />} />
            <Route path="/country-profiles/morocco" element={<Morocco />} />
            <Route path="/country-profiles/cameroon" element={<Cameroon />} />
            <Route path="/country-profiles/kenya" element={<Kenya />} />
            <Route path="/resources/top-5-countries-diaspora-investment-2025" element={<Top5CountriesDiasporaInvestment />} />
            <Route path="/resources/business-registration-ghana-nigeria" element={<BusinessRegistrationGhanaNigeria />} />
            <Route path="/resources/diaspora-startups-changing-africa" element={<DiasporaStartupsChangingAfrica />} />
            <Route path="/resources/insights-doing-business-africa" element={<InsightsDoingBusinessAfrica />} />
            <Route path="/resources/diaspora-investment-trends" element={<DiasporaInvestmentTrends />} />
            <Route path="/resources/success-story-ghana" element={<SuccessStoryGhana />} />

            <Route path="/book-consultation" element={<BookConsultation />} />
            <Route path="/payment" element={<Payment />} />
            <Route path="/privacy-policy" element={<PrivacyPolicy />} />
            <Route path="/terms-of-service" element={<TermsOfService />} />
            <Route path="/admin" element={<Admin />} />
          </Routes>
        </main>
        <Footer />
      </div>
    </Router>
  );
}

export default App;