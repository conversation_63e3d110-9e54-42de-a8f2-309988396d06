// Configuration EmailJS pour AfriPath Consulting
export const EMAILJS_CONFIG = {
  PUBLIC_KEY: 'dEsaYguqIdfJ9XyQ-',
  SERVICE_ID: 'service_d81dmxk',
  TEMPLATE_ID_FREE: 'template_t9s5wgf', // Template pour les services gratuits
  TEMPLATE_ID_PAID: 'template_dskuu7s', // Template pour les services payants
  TO_EMAIL: '<EMAIL>'
};

// Configuration reCAPTCHA
export const RECAPTCHA_CONFIG = {
  SITE_KEY: '6LfQZaQrAAAAABP8D4xCw_CBrWcIWC7kzAI8Bt5w', // Clé publique fournie
  // Note: La clé secrète doit être configurée dans EmailJS dashboard

  // Mode développement - permet de désactiver reCAPTCHA temporairement
  DEVELOPMENT_MODE: false, // Mettre à true pour désactiver reCAPTCHA en développement
};

// Types pour les données du formulaire
export interface ConsultationFormData {
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
  company?: string;
  country: string;
  investmentAmount: string;
  businessType: string;
  timeline: string;
  experience: string;
  message: string;
}

// Template des variables pour EmailJS
export interface EmailTemplateParams {
  to_email: string;
  from_name: string;
  from_email: string;
  subject: string;
  first_name: string;
  last_name: string;
  phone: string;
  company: string;
  country: string;
  investment_amount: string;
  business_type: string;
  timeline: string;
  experience: string;
  message: string;
  reply_to: string;
}
