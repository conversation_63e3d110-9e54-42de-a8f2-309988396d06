# Configuration reCAPTCHA pour AfriPath Consulting

## Étapes de configuration

### 1. Créer un site reCAPTCHA Google

1. <PERSON><PERSON> <PERSON> https://www.google.com/recaptcha
2. Cliquer sur "Admin Console" (en haut à droite)
3. <PERSON><PERSON><PERSON> sur "Create" ou "Register a new site"
4. Remplir les informations :
   - **Label** : AfriPath Consulting
   - **reCAPTCHA type** : Sélectionner "reCAPTCHA v2" puis cocher "I'm not a robot" Checkbox
   - **Domains** : Ajouter les domaines suivants (un par ligne) :
     ```
     localhost
     afripath-consulting.pages.dev
     ```
     (Ajoutez votre domaine personnalisé si vous en avez un)

5. Accepter les conditions d'utilisation
6. Cliquer sur "Submit"

### 2. Récupérer les clés

Après la création, vous obtiendrez deux clés :
- **Site Key** (clé publique) : `6LfQZaQrAAAAABP8D4xCw_CBrWcIWC7kzAI8Bt5w` (déjà configurée)
- **Secret Key** (clé privée) : À utiliser dans EmailJS (voir étape suivante)

### 3. Configurer EmailJS

1. Aller sur https://dashboard.emailjs.com/admin/templates
2. Sélectionner votre template : `template_t9s5wgf`
3. Cliquer sur l'onglet "Settings"
4. Cocher "Enable reCAPTCHA V2 verification"
5. Dans le champ "Secret Key", coller la clé secrète obtenue à l'étape 2
6. Cliquer sur "Save"

### 4. Tester l'implémentation

1. Redémarrer le serveur de développement si nécessaire
2. Aller sur http://localhost:5174/book-consultation
3. Remplir le formulaire
4. Compléter le reCAPTCHA
5. Soumettre le formulaire

## Structure du code

### Configuration (src/config/emailjs.ts)
```typescript
export const RECAPTCHA_CONFIG = {
  SITE_KEY: '6LfQZaQrAAAAABP8D4xCw_CBrWcIWC7kzAI8Bt5w',
};
```

### Implémentation (src/pages/BookConsultation.tsx)
- Import du composant `ReCAPTCHA` de `react-google-recaptcha`
- État pour gérer le token reCAPTCHA
- Fonctions de gestion des événements reCAPTCHA
- Intégration du token dans l'envoi EmailJS avec la propriété `'g-recaptcha-response'`

## Fonctionnalités implémentées

✅ Composant reCAPTCHA v2 intégré au formulaire
✅ Validation côté client (bouton désactivé sans reCAPTCHA)
✅ Gestion des événements (onChange, onExpired, onError)
✅ Reset automatique du reCAPTCHA après soumission
✅ Messages d'aide pour l'utilisateur
✅ Intégration avec EmailJS selon la documentation officielle

## Dépannage

### Erreur 400 lors de l'envoi
- Vérifier que le reCAPTCHA est activé dans le template EmailJS
- Vérifier que la clé secrète est correctement configurée dans EmailJS
- Vérifier que le domaine est autorisé dans la console Google reCAPTCHA

### reCAPTCHA ne s'affiche pas
- Vérifier que la clé publique est correcte
- Vérifier que le domaine est autorisé
- Vérifier la console du navigateur pour les erreurs

### Token expiré
- Le token reCAPTCHA expire après 2 minutes
- L'utilisateur doit refaire la vérification si le token expire
- Le code gère automatiquement l'expiration
